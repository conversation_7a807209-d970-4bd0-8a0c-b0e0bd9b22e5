#!/bin/bash

echo "🚀 بدء كومبايل نظام إدارة قاعدة بيانات الطلبة..."
echo

# التحقق من وجود g++
if ! command -v g++ &> /dev/null; then
    echo "❌ خطأ: g++ غير مثبت"
    echo "يرجى تثبيت build-essential على Ubuntu/Debian:"
    echo "sudo apt-get install build-essential"
    exit 1
fi

# التحقق من وجود SQLite
if ! pkg-config --exists sqlite3; then
    echo "⚠️ تحذير: SQLite development files غير مثبتة"
    echo "يرجى تثبيتها:"
    echo "Ubuntu/Debian: sudo apt-get install libsqlite3-dev"
    echo "macOS: brew install sqlite3"
    echo
fi

# التحقق من وجود ملفات المصدر
if [ ! -f "main.cpp" ]; then
    echo "❌ خطأ: ملف main.cpp غير موجود"
    exit 1
fi

if [ ! -f "student_database.cpp" ]; then
    echo "❌ خطأ: ملف student_database.cpp غير موجود"
    exit 1
fi

if [ ! -f "student_database.h" ]; then
    echo "❌ خطأ: ملف student_database.h غير موجود"
    exit 1
fi

echo "⚙️ كومبايل student_database.cpp..."
if ! g++ -std=c++17 -Wall -Wextra -O2 -c student_database.cpp -o student_database.o; then
    echo "❌ فشل في كومبايل student_database.cpp"
    exit 1
fi

echo "⚙️ كومبايل main.cpp..."
if ! g++ -std=c++17 -Wall -Wextra -O2 -c main.cpp -o main.o; then
    echo "❌ فشل في كومبايل main.cpp"
    exit 1
fi

echo "🔗 ربط الملفات..."
if ! g++ student_database.o main.o -o student_manager -lsqlite3; then
    echo "❌ فشل في ربط الملفات"
    echo "تأكد من تثبيت SQLite development library"
    exit 1
fi

echo "✅ تم الكومبايل بنجاح!"
echo "📁 تم إنشاء الملف التنفيذي: student_manager"

# تنظيف الملفات المؤقتة
rm -f student_database.o main.o

echo
echo "🎯 لتشغيل البرنامج، اكتب: ./student_manager"
echo

# جعل الملف قابل للتنفيذ
chmod +x student_manager

echo "🚀 تشغيل البرنامج..."
./student_manager
