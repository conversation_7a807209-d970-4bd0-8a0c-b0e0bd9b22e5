-- بروم<PERSON><PERSON> لإنشاء قاعدة بيانات SQLite لإدارة مشاريع الطلبة البرمجية

-- جدول الطلبة
CREATE TABLE IF NOT EXISTS students (
    student_id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name TEXT NOT NULL,
    university_id TEXT UNIQUE NOT NULL,
    faculty TEXT NOT NULL,
    department TEXT NOT NULL,
    academic_year INTEGER CHECK (academic_year > 0),
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المشاريع البرمجية
CREATE TABLE IF NOT EXISTS projects (
    project_id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_name TEXT NOT NULL,
    description TEXT,
    programming_language TEXT DEFAULT 'C++',
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عمليات الكومبايل
CREATE TABLE IF NOT EXISTS compilations (
    compilation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    project_id INTEGER NOT NULL,
    compilation_command TEXT DEFAULT 'g++ -std=c++11 -Wall -o',
    source_files TEXT NOT NULL,  -- يمكن تخزين أسماء الملفات مفصولة بفواصل
    output_file TEXT,
    compilation_status TEXT CHECK (compilation_status IN ('success', 'failed', 'warning')),
    errors TEXT,
    warnings TEXT,
    compilation_time REAL,  -- الوقت المستغرق بالثواني
    compilation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (project_id) REFERENCES projects(project_id)
);

-- جدول لتسجيل جلسات العمل
CREATE TABLE IF NOT EXISTS work_sessions (
    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    project_id INTEGER NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    lines_added INTEGER DEFAULT 0,
    lines_removed INTEGER DEFAULT 0,
    files_modified TEXT,
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (project_id) REFERENCES projects(project_id)
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_student_university_id ON students(university_id);
CREATE INDEX IF NOT EXISTS idx_compilation_student ON compilations(student_id);
CREATE INDEX IF NOT EXISTS idx_compilation_project ON compilations(project_id);
CREATE INDEX IF NOT EXISTS idx_compilation_date ON compilations(compilation_date);

-- إضافة بيانات أولية للاختبار
INSERT OR IGNORE INTO students (full_name, university_id, faculty, department, academic_year, email, phone)
VALUES 
    ('أحمد محمد علي', '202010001', 'هندسة', 'حاسوب', 3, '<EMAIL>', '0123456789'),
    ('فاطمة أحمد', '202010002', 'هندسة', 'حاسوب', 2, '<EMAIL>', '0123456788'),
    ('محمد حسن', '202010003', 'علوم', 'رياضيات', 4, '<EMAIL>', '0123456787');

INSERT OR IGNORE INTO projects (project_name, description, programming_language)
VALUES 
    ('نظام إدارة المكتبات', 'نظام لإدارة الكتب والمستعيرين في المكتبة', 'C++'),
    ('آلة حاسبة متقدمة', 'آلة حاسبة تدعم العمليات المعقدة', 'C++'),
    ('لعبة الثعبان', 'لعبة الثعبان الكلاسيكية', 'C++');
