@echo off
echo 🚀 بدء كومبايل نظام إدارة قاعدة بيانات الطلبة...
echo.

REM التحقق من وجود g++
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: g++ غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت MinGW-w64 أو MSYS2
    pause
    exit /b 1
)

REM التحقق من وجود ملفات المصدر
if not exist "main.cpp" (
    echo ❌ خطأ: ملف main.cpp غير موجود
    pause
    exit /b 1
)

if not exist "student_database.cpp" (
    echo ❌ خطأ: ملف student_database.cpp غير موجود
    pause
    exit /b 1
)

if not exist "student_database.h" (
    echo ❌ خطأ: ملف student_database.h غير موجود
    pause
    exit /b 1
)

echo ⚙️ كومبايل student_database.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -c student_database.cpp -o student_database.o
if %errorlevel% neq 0 (
    echo ❌ فشل في كومبايل student_database.cpp
    pause
    exit /b 1
)

echo ⚙️ كومبايل main.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -c main.cpp -o main.o
if %errorlevel% neq 0 (
    echo ❌ فشل في كومبايل main.cpp
    pause
    exit /b 1
)

echo 🔗 ربط الملفات...
g++ student_database.o main.o -o student_manager.exe -lsqlite3
if %errorlevel% neq 0 (
    echo ❌ فشل في ربط الملفات
    echo تأكد من تثبيت SQLite library
    pause
    exit /b 1
)

echo ✅ تم الكومبايل بنجاح!
echo 📁 تم إنشاء الملف التنفيذي: student_manager.exe

REM تنظيف الملفات المؤقتة
if exist "student_database.o" del student_database.o
if exist "main.o" del main.o

echo.
echo 🎯 لتشغيل البرنامج، اكتب: student_manager.exe
echo أو اضغط أي مفتاح لتشغيله الآن...
pause >nul

echo.
echo 🚀 تشغيل البرنامج...
student_manager.exe
