#ifndef STUDENT_DATABASE_H
#define STUDENT_DATABASE_H

#include <sqlite3.h>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>

// هياكل البيانات
struct Student {
    int student_id;
    std::string full_name;
    std::string university_id;
    std::string faculty;
    std::string department;
    int academic_year;
    std::string email;
    std::string phone;
    std::string registration_date;
};

struct Project {
    int project_id;
    std::string project_name;
    std::string description;
    std::string programming_language;
    std::string creation_date;
    std::string last_modified;
};

struct Compilation {
    int compilation_id;
    int student_id;
    int project_id;
    std::string compilation_command;
    std::string source_files;
    std::string output_file;
    std::string compilation_status;
    std::string errors;
    std::string warnings;
    double compilation_time;
    std::string compilation_date;
};

struct WorkSession {
    int session_id;
    int student_id;
    int project_id;
    std::string start_time;
    std::string end_time;
    int lines_added;
    int lines_removed;
    std::string files_modified;
};

// فئة إدارة قاعدة البيانات
class StudentDatabase {
private:
    sqlite3* db;
    std::string db_path;
    
    // دوال مساعدة
    bool executeSQL(const std::string& sql);
    std::string getCurrentTimestamp();
    void printSeparator();
    
public:
    // البناء والهدم
    StudentDatabase(const std::string& database_path = "students.db");
    ~StudentDatabase();
    
    // إدارة قاعدة البيانات
    bool initializeDatabase();
    bool isConnected() const;
    
    // عمليات الطلبة
    bool addStudent(const Student& student);
    bool updateStudent(int student_id, const Student& student);
    bool deleteStudent(int student_id);
    std::vector<Student> getAllStudents();
    Student getStudentById(int student_id);
    Student getStudentByUniversityId(const std::string& university_id);
    
    // عمليات المشاريع
    bool addProject(const Project& project);
    bool updateProject(int project_id, const Project& project);
    bool deleteProject(int project_id);
    std::vector<Project> getAllProjects();
    Project getProjectById(int project_id);
    
    // عمليات الكومبايل
    bool addCompilation(const Compilation& compilation);
    std::vector<Compilation> getCompilationsByStudent(int student_id);
    std::vector<Compilation> getCompilationsByProject(int project_id);
    std::vector<Compilation> getAllCompilations();
    
    // عمليات جلسات العمل
    bool startWorkSession(int student_id, int project_id);
    bool endWorkSession(int session_id, int lines_added = 0, int lines_removed = 0, const std::string& files_modified = "");
    std::vector<WorkSession> getWorkSessionsByStudent(int student_id);
    
    // تقارير وإحصائيات
    void printStudentReport(int student_id);
    void printProjectReport(int project_id);
    void printCompilationStatistics();
    void printAllStudents();
    void printAllProjects();
    void printAllCompilations();
    
    // دوال مساعدة للكومبايل
    bool compileProject(int student_id, int project_id, const std::string& source_files, 
                       const std::string& output_file = "", const std::string& compile_flags = "-std=c++17 -Wall");
};

#endif // STUDENT_DATABASE_H
