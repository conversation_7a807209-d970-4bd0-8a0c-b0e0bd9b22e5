#include "student_database.h"
#include <iostream>
#include <string>
#include <limits>

void clearScreen() {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void waitForEnter() {
    std::cout << "\nاضغط Enter للمتابعة...";
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    std::cin.get();
}

void printMainMenu() {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "🎓 نظام إدارة قاعدة بيانات الطلبة والمشاريع البرمجية" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "1️⃣  إدارة الطلبة" << std::endl;
    std::cout << "2️⃣  إدارة المشاريع" << std::endl;
    std::cout << "3️⃣  عمليات الكومبايل" << std::endl;
    std::cout << "4️⃣  التقارير والإحصائيات" << std::endl;
    std::cout << "5️⃣  عرض جميع البيانات" << std::endl;
    std::cout << "0️⃣  خروج" << std::endl;
    std::cout << std::string(60, '-') << std::endl;
    std::cout << "اختر الخيار: ";
}

void printStudentMenu() {
    std::cout << "\n📚 إدارة الطلبة" << std::endl;
    std::cout << "1. إضافة طالب جديد" << std::endl;
    std::cout << "2. عرض جميع الطلبة" << std::endl;
    std::cout << "3. البحث عن طالب" << std::endl;
    std::cout << "0. العودة للقائمة الرئيسية" << std::endl;
    std::cout << "اختر الخيار: ";
}

void printProjectMenu() {
    std::cout << "\n💻 إدارة المشاريع" << std::endl;
    std::cout << "1. إضافة مشروع جديد" << std::endl;
    std::cout << "2. عرض جميع المشاريع" << std::endl;
    std::cout << "0. العودة للقائمة الرئيسية" << std::endl;
    std::cout << "اختر الخيار: ";
}

void printCompileMenu() {
    std::cout << "\n⚙️ عمليات الكومبايل" << std::endl;
    std::cout << "1. كومبايل مشروع" << std::endl;
    std::cout << "2. عرض سجل الكومبايل" << std::endl;
    std::cout << "0. العودة للقائمة الرئيسية" << std::endl;
    std::cout << "اختر الخيار: ";
}

void addNewStudent(StudentDatabase& db) {
    Student student;
    
    std::cout << "\n➕ إضافة طالب جديد" << std::endl;
    std::cout << std::string(30, '-') << std::endl;
    
    std::cin.ignore(); // تنظيف buffer
    
    std::cout << "الاسم الكامل: ";
    std::getline(std::cin, student.full_name);
    
    std::cout << "الرقم الجامعي: ";
    std::getline(std::cin, student.university_id);
    
    std::cout << "الكلية: ";
    std::getline(std::cin, student.faculty);
    
    std::cout << "القسم: ";
    std::getline(std::cin, student.department);
    
    std::cout << "السنة الدراسية: ";
    std::cin >> student.academic_year;
    std::cin.ignore();
    
    std::cout << "البريد الإلكتروني: ";
    std::getline(std::cin, student.email);
    
    std::cout << "رقم الهاتف: ";
    std::getline(std::cin, student.phone);
    
    if (db.addStudent(student)) {
        std::cout << "✅ تم إضافة الطالب بنجاح!" << std::endl;
    } else {
        std::cout << "❌ فشل في إضافة الطالب!" << std::endl;
    }
}

void addNewProject(StudentDatabase& db) {
    Project project;
    
    std::cout << "\n➕ إضافة مشروع جديد" << std::endl;
    std::cout << std::string(30, '-') << std::endl;
    
    std::cin.ignore(); // تنظيف buffer
    
    std::cout << "اسم المشروع: ";
    std::getline(std::cin, project.project_name);
    
    std::cout << "وصف المشروع: ";
    std::getline(std::cin, project.description);
    
    std::cout << "لغة البرمجة (افتراضي: C++): ";
    std::getline(std::cin, project.programming_language);
    if (project.programming_language.empty()) {
        project.programming_language = "C++";
    }
    
    if (db.addProject(project)) {
        std::cout << "✅ تم إضافة المشروع بنجاح!" << std::endl;
    } else {
        std::cout << "❌ فشل في إضافة المشروع!" << std::endl;
    }
}

void compileProject(StudentDatabase& db) {
    std::cout << "\n⚙️ كومبايل مشروع" << std::endl;
    std::cout << std::string(30, '-') << std::endl;
    
    int student_id, project_id;
    std::string source_files, output_file, compile_flags;
    
    std::cout << "رقم الطالب: ";
    std::cin >> student_id;
    
    std::cout << "رقم المشروع: ";
    std::cin >> project_id;
    
    std::cin.ignore(); // تنظيف buffer
    
    std::cout << "ملفات المصدر (مفصولة بمسافات): ";
    std::getline(std::cin, source_files);
    
    std::cout << "اسم الملف التنفيذي (اختياري): ";
    std::getline(std::cin, output_file);
    
    std::cout << "خيارات الكومبايل (افتراضي: -std=c++17 -Wall): ";
    std::getline(std::cin, compile_flags);
    if (compile_flags.empty()) {
        compile_flags = "-std=c++17 -Wall";
    }
    
    db.compileProject(student_id, project_id, source_files, output_file, compile_flags);
}

int main() {
    std::cout << "🚀 بدء تشغيل نظام إدارة قاعدة بيانات الطلبة..." << std::endl;

    StudentDatabase db("students.db");

    if (!db.isConnected()) {
        std::cerr << "❌ فشل في الاتصال بقاعدة البيانات!" << std::endl;
        return 1;
    }

    if (!db.initializeDatabase()) {
        std::cerr << "❌ فشل في تهيئة قاعدة البيانات!" << std::endl;
        return 1;
    }

    int choice;

    while (true) {
        clearScreen();
        printMainMenu();
        std::cin >> choice;

        switch (choice) {
            case 1: { // إدارة الطلبة
                int studentChoice;
                do {
                    clearScreen();
                    printStudentMenu();
                    std::cin >> studentChoice;

                    switch (studentChoice) {
                        case 1:
                            addNewStudent(db);
                            waitForEnter();
                            break;
                        case 2:
                            db.printAllStudents();
                            waitForEnter();
                            break;
                        case 3: {
                            int student_id;
                            std::cout << "أدخل رقم الطالب: ";
                            std::cin >> student_id;
                            Student student = db.getStudentById(student_id);
                            if (student.student_id != 0) {
                                std::cout << "\n📋 بيانات الطالب:" << std::endl;
                                std::cout << "الاسم: " << student.full_name << std::endl;
                                std::cout << "الرقم الجامعي: " << student.university_id << std::endl;
                                std::cout << "الكلية: " << student.faculty << std::endl;
                                std::cout << "القسم: " << student.department << std::endl;
                            } else {
                                std::cout << "❌ لم يتم العثور على الطالب!" << std::endl;
                            }
                            waitForEnter();
                            break;
                        }
                    }
                } while (studentChoice != 0);
                break;
            }

            case 2: { // إدارة المشاريع
                int projectChoice;
                do {
                    clearScreen();
                    printProjectMenu();
                    std::cin >> projectChoice;

                    switch (projectChoice) {
                        case 1:
                            addNewProject(db);
                            waitForEnter();
                            break;
                        case 2:
                            db.printAllProjects();
                            waitForEnter();
                            break;
                    }
                } while (projectChoice != 0);
                break;
            }

            case 3: { // عمليات الكومبايل
                int compileChoice;
                do {
                    clearScreen();
                    printCompileMenu();
                    std::cin >> compileChoice;

                    switch (compileChoice) {
                        case 1:
                            compileProject(db);
                            waitForEnter();
                            break;
                        case 2: {
                            std::vector<Compilation> compilations = db.getAllCompilations();
                            std::cout << "\n📊 سجل عمليات الكومبايل" << std::endl;
                            std::cout << std::string(50, '=') << std::endl;

                            if (compilations.empty()) {
                                std::cout << "لا توجد عمليات كومبايل مسجلة" << std::endl;
                            } else {
                                for (const auto& comp : compilations) {
                                    std::cout << "🆔 رقم العملية: " << comp.compilation_id << std::endl;
                                    std::cout << "👤 رقم الطالب: " << comp.student_id << std::endl;
                                    std::cout << "💻 رقم المشروع: " << comp.project_id << std::endl;
                                    std::cout << "⚙️ الأمر: " << comp.compilation_command << std::endl;
                                    std::cout << "📁 الملفات: " << comp.source_files << std::endl;
                                    std::cout << "📊 الحالة: " << comp.compilation_status << std::endl;
                                    std::cout << "⏱️ الوقت: " << comp.compilation_time << " ثانية" << std::endl;
                                    std::cout << "📅 التاريخ: " << comp.compilation_date << std::endl;
                                    if (!comp.errors.empty()) {
                                        std::cout << "❌ الأخطاء: " << comp.errors << std::endl;
                                    }
                                    std::cout << std::string(30, '-') << std::endl;
                                }
                            }
                            waitForEnter();
                            break;
                        }
                    }
                } while (compileChoice != 0);
                break;
            }

            case 4: { // التقارير والإحصائيات
                std::cout << "\n📊 التقارير والإحصائيات" << std::endl;
                std::cout << "هذه الميزة قيد التطوير..." << std::endl;
                waitForEnter();
                break;
            }

            case 5: { // عرض جميع البيانات
                clearScreen();
                db.printAllStudents();
                std::cout << std::endl;
                db.printAllProjects();
                waitForEnter();
                break;
            }

            case 0:
                std::cout << "\n👋 شكراً لاستخدام النظام!" << std::endl;
                return 0;

            default:
                std::cout << "❌ خيار غير صحيح!" << std::endl;
                waitForEnter();
        }
    }

    return 0;
}
