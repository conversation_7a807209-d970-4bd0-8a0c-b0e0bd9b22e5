# 🎓 نظام إدارة قاعدة بيانات الطلبة والمشاريع البرمجية

نظام شامل لإدارة بيانات الطلبة ومشاريعهم البرمجية مع تتبع عمليات الكومبايل باستخدام C++ و SQLite.

## 🚀 المميزات

- ✅ إدارة بيانات الطلبة (إضافة، عرض، بحث)
- ✅ إدارة المشاريع البرمجية
- ✅ تتبع عمليات الكومبايل باستخدام g++
- ✅ قياس أوقات الكومبايل
- ✅ تسجيل الأخطاء والتحذيرات
- ✅ تقارير وإحصائيات شاملة
- ✅ واجهة مستخدم تفاعلية باللغة العربية

## 📋 المتطلبات

### Windows:
```bash
# تثبيت MinGW-w64 أو MSYS2
# تثبيت SQLite
```

### Linux/Ubuntu:
```bash
sudo apt-get update
sudo apt-get install build-essential sqlite3 libsqlite3-dev
```

### macOS:
```bash
brew install sqlite3
```

## 🛠️ التثبيت والتشغيل

### الطريقة الأولى: استخدام Makefile
```bash
# بناء المشروع
make all

# تشغيل البرنامج
make run

# تنظيف الملفات المؤقتة
make clean

# عرض المساعدة
make help
```

### الطريقة الثانية: الكومبايل اليدوي
```bash
# كومبايل الملفات
g++ -std=c++17 -Wall -Wextra -O2 -c student_database.cpp -o student_database.o
g++ -std=c++17 -Wall -Wextra -O2 -c main.cpp -o main.o

# ربط الملفات
g++ student_database.o main.o -o student_manager -lsqlite3

# تشغيل البرنامج
./student_manager
```

### Windows (باستخدام g++):
```cmd
g++ -std=c++17 -Wall -Wextra -O2 student_database.cpp main.cpp -o student_manager.exe -lsqlite3
student_manager.exe
```

## 📁 هيكل المشروع

```
📦 student-database-system/
├── 📄 main.cpp                 # الملف الرئيسي للتطبيق
├── 📄 student_database.h       # ملف الهيدر
├── 📄 student_database.cpp     # تنفيذ الفئات والدوال
├── 📄 database_schema.sql      # مخطط قاعدة البيانات
├── 📄 Makefile                 # ملف البناء
├── 📄 README.md                # هذا الملف
└── 📄 students.db              # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🗄️ هيكل قاعدة البيانات

### جدول الطلبة (students)
- `student_id`: رقم الطالب (مفتاح أساسي)
- `full_name`: الاسم الكامل
- `university_id`: الرقم الجامعي
- `faculty`: الكلية
- `department`: القسم
- `academic_year`: السنة الدراسية
- `email`: البريد الإلكتروني
- `phone`: رقم الهاتف
- `registration_date`: تاريخ التسجيل

### جدول المشاريع (projects)
- `project_id`: رقم المشروع (مفتاح أساسي)
- `project_name`: اسم المشروع
- `description`: وصف المشروع
- `programming_language`: لغة البرمجة
- `creation_date`: تاريخ الإنشاء
- `last_modified`: تاريخ آخر تعديل

### جدول عمليات الكومبايل (compilations)
- `compilation_id`: رقم عملية الكومبايل
- `student_id`: رقم الطالب
- `project_id`: رقم المشروع
- `compilation_command`: أمر الكومبايل
- `source_files`: ملفات المصدر
- `output_file`: الملف التنفيذي
- `compilation_status`: حالة الكومبايل (success/failed/warning)
- `errors`: الأخطاء
- `warnings`: التحذيرات
- `compilation_time`: وقت الكومبايل بالثواني
- `compilation_date`: تاريخ الكومبايل

## 🎯 كيفية الاستخدام

1. **تشغيل البرنامج**: `./student_manager` أو `make run`
2. **إضافة طالب جديد**: اختر الخيار 1 ثم 1
3. **إضافة مشروع جديد**: اختر الخيار 2 ثم 1
4. **كومبايل مشروع**: اختر الخيار 3 ثم 1
5. **عرض التقارير**: اختر الخيار 4

## 📊 أمثلة على الاستخدام

### إضافة طالب:
```
الاسم الكامل: أحمد محمد علي
الرقم الجامعي: 202010001
الكلية: هندسة
القسم: حاسوب
السنة الدراسية: 3
البريد الإلكتروني: <EMAIL>
رقم الهاتف: 0123456789
```

### كومبايل مشروع:
```
رقم الطالب: 1
رقم المشروع: 1
ملفات المصدر: main.cpp utils.cpp
اسم الملف التنفيذي: my_program
خيارات الكومبايل: -std=c++17 -Wall -O2
```

## 🔧 استكشاف الأخطاء

### خطأ: "لا يمكن العثور على sqlite3"
```bash
# Linux/Ubuntu
sudo apt-get install libsqlite3-dev

# macOS
brew install sqlite3

# Windows
# تأكد من تثبيت SQLite وإضافته لمتغير PATH
```

### خطأ: "فشل في فتح قاعدة البيانات"
- تأكد من وجود صلاحيات الكتابة في المجلد
- تأكد من عدم استخدام قاعدة البيانات من برنامج آخر

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مميزات جديدة
- إصلاح الأخطاء
- تحسين الواجهة
- إضافة تقارير جديدة

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.
