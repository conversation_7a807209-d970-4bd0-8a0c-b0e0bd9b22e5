# Makefile لمشروع نظام إدارة قاعدة بيانات الطلبة

# متغيرات الكومبايل
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
LIBS = -lsqlite3
TARGET = student_manager
SOURCES = main.cpp student_database.cpp
OBJECTS = $(SOURCES:.cpp=.o)
HEADERS = student_database.h

# الهدف الرئيسي
all: $(TARGET)

# بناء الملف التنفيذي
$(TARGET): $(OBJECTS)
	@echo "🔗 ربط الملفات..."
	$(CXX) $(OBJECTS) -o $(TARGET) $(LIBS)
	@echo "✅ تم إنشاء الملف التنفيذي: $(TARGET)"

# كومبايل ملفات .cpp إلى .o
%.o: %.cpp $(HEADERS)
	@echo "⚙️ كومبايل $<..."
	$(CXX) $(CXXFLAGS) -c $< -o $@

# تنظيف الملفات المؤقتة
clean:
	@echo "🧹 تنظيف الملفات المؤقتة..."
	rm -f $(OBJECTS) $(TARGET)
	@echo "✅ تم التنظيف"

# تشغيل البرنامج
run: $(TARGET)
	@echo "🚀 تشغيل البرنامج..."
	./$(TARGET)

# إنشاء قاعدة البيانات
init-db: $(TARGET)
	@echo "🗄️ إنشاء قاعدة البيانات..."
	./$(TARGET)

# تثبيت SQLite (للأنظمة التي تدعم apt)
install-deps:
	@echo "📦 تثبيت SQLite..."
	sudo apt-get update
	sudo apt-get install sqlite3 libsqlite3-dev

# عرض معلومات المشروع
info:
	@echo "📋 معلومات المشروع:"
	@echo "   الاسم: نظام إدارة قاعدة بيانات الطلبة"
	@echo "   اللغة: C++"
	@echo "   قاعدة البيانات: SQLite"
	@echo "   الملفات المصدرية: $(SOURCES)"
	@echo "   الملف التنفيذي: $(TARGET)"

# مساعدة
help:
	@echo "🆘 الأوامر المتاحة:"
	@echo "   make all        - بناء المشروع"
	@echo "   make clean      - تنظيف الملفات المؤقتة"
	@echo "   make run        - تشغيل البرنامج"
	@echo "   make init-db    - إنشاء قاعدة البيانات"
	@echo "   make install-deps - تثبيت SQLite"
	@echo "   make info       - عرض معلومات المشروع"
	@echo "   make help       - عرض هذه المساعدة"

# تحديد الأهداف التي لا تنشئ ملفات
.PHONY: all clean run init-db install-deps info help
