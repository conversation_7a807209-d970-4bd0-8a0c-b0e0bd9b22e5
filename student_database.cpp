#include "student_database.h"
#include <fstream>
#include <cstdlib>
#include <ctime>

// البناء والهدم
StudentDatabase::StudentDatabase(const std::string& database_path) : db(nullptr), db_path(database_path) {
    int rc = sqlite3_open(database_path.c_str(), &db);
    if (rc) {
        std::cerr << "خطأ في فتح قاعدة البيانات: " << sqlite3_errmsg(db) << std::endl;
        db = nullptr;
    } else {
        std::cout << "تم الاتصال بقاعدة البيانات بنجاح: " << database_path << std::endl;
    }
}

StudentDatabase::~StudentDatabase() {
    if (db) {
        sqlite3_close(db);
        std::cout << "تم إغلاق الاتصال بقاعدة البيانات" << std::endl;
    }
}

// دوال مساعدة
bool StudentDatabase::executeSQL(const std::string& sql) {
    if (!db) return false;
    
    char* errMsg = 0;
    int rc = sqlite3_exec(db, sql.c_str(), 0, 0, &errMsg);
    
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تنفيذ SQL: " << errMsg << std::endl;
        sqlite3_free(errMsg);
        return false;
    }
    return true;
}

std::string StudentDatabase::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

void StudentDatabase::printSeparator() {
    std::cout << std::string(80, '=') << std::endl;
}

bool StudentDatabase::isConnected() const {
    return db != nullptr;
}

// إدارة قاعدة البيانات
bool StudentDatabase::initializeDatabase() {
    if (!db) return false;
    
    std::ifstream schema_file("database_schema.sql");
    if (!schema_file.is_open()) {
        std::cerr << "خطأ: لا يمكن فتح ملف المخطط database_schema.sql" << std::endl;
        return false;
    }
    
    std::string sql_content((std::istreambuf_iterator<char>(schema_file)),
                            std::istreambuf_iterator<char>());
    schema_file.close();
    
    // تقسيم الاستعلامات وتنفيذها واحداً تلو الآخر
    std::stringstream ss(sql_content);
    std::string statement;
    
    while (std::getline(ss, statement, ';')) {
        // إزالة المسافات والأسطر الفارغة
        statement.erase(0, statement.find_first_not_of(" \t\n\r"));
        statement.erase(statement.find_last_not_of(" \t\n\r") + 1);
        
        if (!statement.empty() && statement.substr(0, 2) != "--") {
            statement += ";";
            if (!executeSQL(statement)) {
                std::cerr << "خطأ في تنفيذ الاستعلام: " << statement << std::endl;
                return false;
            }
        }
    }
    
    std::cout << "تم إنشاء قاعدة البيانات وتهيئتها بنجاح!" << std::endl;
    return true;
}

// عمليات الطلبة
bool StudentDatabase::addStudent(const Student& student) {
    if (!db) return false;
    
    const char* sql = R"(
        INSERT INTO students (full_name, university_id, faculty, department, academic_year, email, phone)
        VALUES (?, ?, ?, ?, ?, ?, ?);
    )";
    
    sqlite3_stmt* stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
    
    sqlite3_bind_text(stmt, 1, student.full_name.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, student.university_id.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, student.faculty.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 4, student.department.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 5, student.academic_year);
    sqlite3_bind_text(stmt, 6, student.email.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 7, student.phone.c_str(), -1, SQLITE_STATIC);
    
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    
    if (rc == SQLITE_DONE) {
        std::cout << "تم إضافة الطالب بنجاح: " << student.full_name << std::endl;
        return true;
    } else {
        std::cerr << "خطأ في إضافة الطالب: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
}

std::vector<Student> StudentDatabase::getAllStudents() {
    std::vector<Student> students;
    if (!db) return students;

    const char* sql = "SELECT * FROM students ORDER BY student_id;";
    sqlite3_stmt* stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return students;
    }

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        Student student;
        student.student_id = sqlite3_column_int(stmt, 0);
        student.full_name = (char*)sqlite3_column_text(stmt, 1);
        student.university_id = (char*)sqlite3_column_text(stmt, 2);
        student.faculty = (char*)sqlite3_column_text(stmt, 3);
        student.department = (char*)sqlite3_column_text(stmt, 4);
        student.academic_year = sqlite3_column_int(stmt, 5);
        student.email = (char*)sqlite3_column_text(stmt, 6);
        student.phone = sqlite3_column_text(stmt, 7) ? (char*)sqlite3_column_text(stmt, 7) : "";
        student.registration_date = (char*)sqlite3_column_text(stmt, 8);
        students.push_back(student);
    }

    sqlite3_finalize(stmt);
    return students;
}

Student StudentDatabase::getStudentById(int student_id) {
    Student student = {};
    if (!db) return student;

    const char* sql = "SELECT * FROM students WHERE student_id = ?;";
    sqlite3_stmt* stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return student;
    }

    sqlite3_bind_int(stmt, 1, student_id);

    if (sqlite3_step(stmt) == SQLITE_ROW) {
        student.student_id = sqlite3_column_int(stmt, 0);
        student.full_name = (char*)sqlite3_column_text(stmt, 1);
        student.university_id = (char*)sqlite3_column_text(stmt, 2);
        student.faculty = (char*)sqlite3_column_text(stmt, 3);
        student.department = (char*)sqlite3_column_text(stmt, 4);
        student.academic_year = sqlite3_column_int(stmt, 5);
        student.email = (char*)sqlite3_column_text(stmt, 6);
        student.phone = sqlite3_column_text(stmt, 7) ? (char*)sqlite3_column_text(stmt, 7) : "";
        student.registration_date = (char*)sqlite3_column_text(stmt, 8);
    }

    sqlite3_finalize(stmt);
    return student;
}

// عمليات المشاريع
bool StudentDatabase::addProject(const Project& project) {
    if (!db) return false;

    const char* sql = R"(
        INSERT INTO projects (project_name, description, programming_language)
        VALUES (?, ?, ?);
    )";

    sqlite3_stmt* stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);

    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }

    sqlite3_bind_text(stmt, 1, project.project_name.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, project.description.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, project.programming_language.c_str(), -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    if (rc == SQLITE_DONE) {
        std::cout << "تم إضافة المشروع بنجاح: " << project.project_name << std::endl;
        return true;
    } else {
        std::cerr << "خطأ في إضافة المشروع: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
}

std::vector<Project> StudentDatabase::getAllProjects() {
    std::vector<Project> projects;
    if (!db) return projects;

    const char* sql = "SELECT * FROM projects ORDER BY project_id;";
    sqlite3_stmt* stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return projects;
    }

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        Project project;
        project.project_id = sqlite3_column_int(stmt, 0);
        project.project_name = (char*)sqlite3_column_text(stmt, 1);
        project.description = sqlite3_column_text(stmt, 2) ? (char*)sqlite3_column_text(stmt, 2) : "";
        project.programming_language = (char*)sqlite3_column_text(stmt, 3);
        project.creation_date = (char*)sqlite3_column_text(stmt, 4);
        project.last_modified = (char*)sqlite3_column_text(stmt, 5);
        projects.push_back(project);
    }

    sqlite3_finalize(stmt);
    return projects;
}

// عمليات الكومبايل
bool StudentDatabase::addCompilation(const Compilation& compilation) {
    if (!db) return false;

    const char* sql = R"(
        INSERT INTO compilations (student_id, project_id, compilation_command, source_files,
                                output_file, compilation_status, errors, warnings, compilation_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
    )";

    sqlite3_stmt* stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);

    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }

    sqlite3_bind_int(stmt, 1, compilation.student_id);
    sqlite3_bind_int(stmt, 2, compilation.project_id);
    sqlite3_bind_text(stmt, 3, compilation.compilation_command.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 4, compilation.source_files.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 5, compilation.output_file.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 6, compilation.compilation_status.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 7, compilation.errors.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 8, compilation.warnings.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_double(stmt, 9, compilation.compilation_time);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    if (rc == SQLITE_DONE) {
        std::cout << "تم تسجيل عملية الكومبايل بنجاح" << std::endl;
        return true;
    } else {
        std::cerr << "خطأ في تسجيل عملية الكومبايل: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
}

// دالة كومبايل المشروع
bool StudentDatabase::compileProject(int student_id, int project_id, const std::string& source_files,
                                   const std::string& output_file, const std::string& compile_flags) {
    if (!db) return false;

    // إنشاء أمر الكومبايل
    std::string output_name = output_file.empty() ? "program" : output_file;
    std::string compile_command = "g++ " + compile_flags + " " + source_files + " -o " + output_name;

    std::cout << "تنفيذ أمر الكومبايل: " << compile_command << std::endl;

    // قياس وقت الكومبايل
    auto start_time = std::chrono::high_resolution_clock::now();
    int result = std::system(compile_command.c_str());
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double compilation_time = duration.count() / 1000.0;

    // تحديد حالة الكومبايل
    std::string status = (result == 0) ? "success" : "failed";
    std::string errors = (result != 0) ? "Compilation failed with exit code: " + std::to_string(result) : "";

    // تسجيل عملية الكومبايل في قاعدة البيانات
    Compilation compilation;
    compilation.student_id = student_id;
    compilation.project_id = project_id;
    compilation.compilation_command = compile_command;
    compilation.source_files = source_files;
    compilation.output_file = output_name;
    compilation.compilation_status = status;
    compilation.errors = errors;
    compilation.warnings = "";
    compilation.compilation_time = compilation_time;

    bool db_result = addCompilation(compilation);

    if (result == 0) {
        std::cout << "✅ تم الكومبايل بنجاح في " << compilation_time << " ثانية" << std::endl;
    } else {
        std::cout << "❌ فشل الكومبايل" << std::endl;
    }

    return result == 0 && db_result;
}

// دوال التقارير
void StudentDatabase::printAllStudents() {
    std::vector<Student> students = getAllStudents();

    printSeparator();
    std::cout << "📋 قائمة جميع الطلبة" << std::endl;
    printSeparator();

    if (students.empty()) {
        std::cout << "لا توجد بيانات طلبة" << std::endl;
        return;
    }

    for (const auto& student : students) {
        std::cout << "🆔 رقم الطالب: " << student.student_id << std::endl;
        std::cout << "👤 الاسم: " << student.full_name << std::endl;
        std::cout << "🎓 الرقم الجامعي: " << student.university_id << std::endl;
        std::cout << "🏫 الكلية: " << student.faculty << std::endl;
        std::cout << "📚 القسم: " << student.department << std::endl;
        std::cout << "📅 السنة الدراسية: " << student.academic_year << std::endl;
        std::cout << "📧 البريد الإلكتروني: " << student.email << std::endl;
        std::cout << "📱 الهاتف: " << student.phone << std::endl;
        std::cout << "📅 تاريخ التسجيل: " << student.registration_date << std::endl;
        std::cout << std::string(40, '-') << std::endl;
    }
}

void StudentDatabase::printAllProjects() {
    std::vector<Project> projects = getAllProjects();

    printSeparator();
    std::cout << "💻 قائمة جميع المشاريع" << std::endl;
    printSeparator();

    if (projects.empty()) {
        std::cout << "لا توجد مشاريع" << std::endl;
        return;
    }

    for (const auto& project : projects) {
        std::cout << "🆔 رقم المشروع: " << project.project_id << std::endl;
        std::cout << "📝 اسم المشروع: " << project.project_name << std::endl;
        std::cout << "📄 الوصف: " << project.description << std::endl;
        std::cout << "💻 لغة البرمجة: " << project.programming_language << std::endl;
        std::cout << "📅 تاريخ الإنشاء: " << project.creation_date << std::endl;
        std::cout << "🔄 آخر تعديل: " << project.last_modified << std::endl;
        std::cout << std::string(40, '-') << std::endl;
    }
}

std::vector<Compilation> StudentDatabase::getAllCompilations() {
    std::vector<Compilation> compilations;
    if (!db) return compilations;

    const char* sql = R"(
        SELECT c.*, s.full_name, p.project_name
        FROM compilations c
        JOIN students s ON c.student_id = s.student_id
        JOIN projects p ON c.project_id = p.project_id
        ORDER BY c.compilation_date DESC;
    )";

    sqlite3_stmt* stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);

    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في تحضير الاستعلام: " << sqlite3_errmsg(db) << std::endl;
        return compilations;
    }

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        Compilation compilation;
        compilation.compilation_id = sqlite3_column_int(stmt, 0);
        compilation.student_id = sqlite3_column_int(stmt, 1);
        compilation.project_id = sqlite3_column_int(stmt, 2);
        compilation.compilation_command = (char*)sqlite3_column_text(stmt, 3);
        compilation.source_files = (char*)sqlite3_column_text(stmt, 4);
        compilation.output_file = sqlite3_column_text(stmt, 5) ? (char*)sqlite3_column_text(stmt, 5) : "";
        compilation.compilation_status = (char*)sqlite3_column_text(stmt, 6);
        compilation.errors = sqlite3_column_text(stmt, 7) ? (char*)sqlite3_column_text(stmt, 7) : "";
        compilation.warnings = sqlite3_column_text(stmt, 8) ? (char*)sqlite3_column_text(stmt, 8) : "";
        compilation.compilation_time = sqlite3_column_double(stmt, 9);
        compilation.compilation_date = (char*)sqlite3_column_text(stmt, 10);
        compilations.push_back(compilation);
    }

    sqlite3_finalize(stmt);
    return compilations;
}
